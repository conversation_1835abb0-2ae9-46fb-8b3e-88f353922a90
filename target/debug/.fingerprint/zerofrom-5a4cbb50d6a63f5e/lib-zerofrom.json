{"rustc": 6560579996391851404, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 5347358027863023418, "path": 2624717793040994153, "deps": [[4022439902832367970, "zerofrom_derive", false, 12064947239396023892]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-5a4cbb50d6a63f5e/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}