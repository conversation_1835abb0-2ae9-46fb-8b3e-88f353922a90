{"rustc": 6560579996391851404, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 6728679644652379912, "deps": [[4684437522915235464, "libc", false, 13396783399387063195], [7896293946984509699, "bitflags", false, 390787459793450437], [8253628577145923712, "libc_errno", false, 1317474243711398217], [12053020504183902936, "build_script_build", false, 4191553145575708049]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-e033a2e45b78d3a0/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}