{"rustc": 6560579996391851404, "features": "[\"add\", \"add_assign\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 3033921117576893, "path": 473046317421185542, "deps": [[3060637413840920116, "proc_macro2", false, 17918266945729557590], [10640660562325816595, "syn", false, 7343010750575165353], [17990358020177143287, "quote", false, 13216528631654810914]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-4086be6f2c00afee/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}