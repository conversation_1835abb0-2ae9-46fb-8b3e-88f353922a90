{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 5347358027863023418, "path": 8535764436691162817, "deps": [[2828590642173593838, "cfg_if", false, 15851487441136097607], [3666196340704888985, "smallvec", false, 12301240383850435757], [4269498962362888130, "build_script_build", false, 11259525877697859117], [4684437522915235464, "libc", false, 13396783399387063195]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-2e125b62de2849ef/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}